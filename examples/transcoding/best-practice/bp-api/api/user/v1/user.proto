syntax = "proto3";

package user.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";

option go_package = "github.com/your/repo/user/v1;user_v1";
option java_multiple_files = true;

message User {
  int64 id = 1;
  string name = 2;
  Gender gender = 3;

  enum Gender {
    GENDER_UNSPECIFIED = 0;
    MALE = 1;
    FEMALE = 2;
  }
}

message GetUserRequest {
  int64 id = 1 [(buf.validate.field).int64 = {gte: 0}];
}

message GetUserResponse {
  User user = 1;
}

message DeleteUserRequest {
  int64 id = 1 [(buf.validate.field).int64 = {gte: 0}];
}

message DeleteUserResponse {}

service UserService {
  // Get a user by ID.
  rpc GetUser(GetUserRequest) returns (GetUserResponse) {
    option (google.api.http) = {get: "/v1/users/{id}"};
  }

  // Delete a user by ID.
  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse) {
    option (google.api.http) = {delete: "/v1/users/{id}"};
  }
}
