// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: user/v1/user.proto

// Protobuf Java Version: 3.25.1
package user.v1;

public final class UserOuterClass {
  private UserOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_user_v1_User_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_user_v1_User_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_user_v1_GetUserRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_user_v1_GetUserRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_user_v1_GetUserResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_user_v1_GetUserResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_user_v1_DeleteUserRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_user_v1_DeleteUserRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_user_v1_DeleteUserResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_user_v1_DeleteUserResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022user/v1/user.proto\022\007user.v1\032\033buf/valid" +
      "ate/validate.proto\032\034google/api/annotatio" +
      "ns.proto\"\220\001\n\004User\022\016\n\002id\030\001 \001(\003R\002id\022\022\n\004nam" +
      "e\030\002 \001(\tR\004name\022,\n\006gender\030\003 \001(\0162\024.user.v1." +
      "User.GenderR\006gender\"6\n\006Gender\022\026\n\022GENDER_" +
      "UNSPECIFIED\020\000\022\010\n\004MALE\020\001\022\n\n\006FEMALE\020\002\")\n\016G" +
      "etUserRequest\022\027\n\002id\030\001 \001(\003B\007\272H\004\"\002(\000R\002id\"4" +
      "\n\017GetUserResponse\022!\n\004user\030\001 \001(\0132\r.user.v" +
      "1.UserR\004user\",\n\021DeleteUserRequest\022\027\n\002id\030" +
      "\001 \001(\003B\007\272H\004\"\002(\000R\002id\"\024\n\022DeleteUserResponse" +
      "2\302\001\n\013UserService\022T\n\007GetUser\022\027.user.v1.Ge" +
      "tUserRequest\032\030.user.v1.GetUserResponse\"\026" +
      "\202\323\344\223\002\020\022\016/v1/users/{id}\022]\n\nDeleteUser\022\032.u" +
      "ser.v1.DeleteUserRequest\032\033.user.v1.Delet" +
      "eUserResponse\"\026\202\323\344\223\002\020*\016/v1/users/{id}B(P" +
      "\001Z$github.com/your/repo/user/v1;user_v1b" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          build.buf.validate.ValidateProto.getDescriptor(),
          com.google.api.AnnotationsProto.getDescriptor(),
        });
    internal_static_user_v1_User_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_user_v1_User_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_user_v1_User_descriptor,
        new java.lang.String[] { "Id", "Name", "Gender", });
    internal_static_user_v1_GetUserRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_user_v1_GetUserRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_user_v1_GetUserRequest_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_user_v1_GetUserResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_user_v1_GetUserResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_user_v1_GetUserResponse_descriptor,
        new java.lang.String[] { "User", });
    internal_static_user_v1_DeleteUserRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_user_v1_DeleteUserRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_user_v1_DeleteUserRequest_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_user_v1_DeleteUserResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_user_v1_DeleteUserResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_user_v1_DeleteUserResponse_descriptor,
        new java.lang.String[] { });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(build.buf.validate.ValidateProto.field);
    registry.add(com.google.api.AnnotationsProto.http);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    build.buf.validate.ValidateProto.getDescriptor();
    com.google.api.AnnotationsProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
